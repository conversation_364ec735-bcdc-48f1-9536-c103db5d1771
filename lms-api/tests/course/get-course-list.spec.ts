import { expect } from '@playwright/test';
import { test } from '../../fixtures/default-fixture';

test.describe('Positive', () => {
  test.skip('@SKL-T16363 Get Course return Course list when token valid', async ({ courseService }) => {
    // Arrange

    // Act
    const resp = await courseService.getAllCourse();

    // Assert
    const respJson = await resp.json();
    await expect(resp).toBeOK();
    expect(respJson).toEqual(
      expect.objectContaining({
        message: 'Success',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              coursecode: expect.any(String),
            }),
          ]),
        }),
      }),
    );
  });

  const searchCases = [
    {
      caseId: '@SKL-T16372',
      course: 'ABC_001',
      case: 'course code',
    },
    {
      caseId: '@SKL-T16373',
      course: 'HAHAHAHAHAHA',
      case: 'course name',
    },
  ];
  for (const searchCase of searchCases) {
    test.skip(`${searchCase.caseId}Get Course return Course list when Search ${searchCase.case} is matched`, async ({
      courseService,
    }) => {
      // Arrange

      // Act
      const resp = await courseService.getCourseListBySearch(`${searchCase.course}`);

      // Assert
      const respJson = await resp.json();
      expect(resp).toBeOK();
      expect(respJson).toEqual(
        expect.objectContaining({
          message: 'Success',
          data: expect.objectContaining({
            items: expect.arrayContaining([
              expect.objectContaining({
                coursecode: 'ABC_001',
                coursename: 'HAHAHAHAHAHA',
              }),
            ]),
          }),
        }),
      );
    });
  }
  test.skip('@SKL-T16374 Get Course return no course data when Search data is not matched', async ({
    courseService,
  }) => {
    // Arrange

    // Act
    const resp = await courseService.getCourseListBySearch('Course is not exist');

    // Assert
    const respJson = await resp.json();
    await expect(resp).toBeOK();
    expect(respJson).toEqual(
      expect.objectContaining({
        message: 'Success',
        data: expect.objectContaining({
          items: expect.arrayContaining([]),
        }),
      }),
    );
  });

  test(`User with valid permission get list of course should return success`, async ({
    configuration,
    authenticationService,
    courseService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const getCourseList = await courseService.getCourseList();
    await expect(getCourseList).toBeOK();
    const getCourseListResp = await getCourseList.json();
    expect(getCourseListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.objectContaining({
          total: expect.any(Number),
        }),
      }),
    );
    expect(getCourseListResp.data.total).toBeGreaterThan(0);
  });

  test('@SKL-T20190 Admin filter ข้อมูลรายการหลักสูตร, แสดงผลรายการหลักสูตรตามการ filter', async ({
    configuration,
    authenticationService,
    courseService,
  }) => {
    // Arrange
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    // Define comprehensive filter parameters based on the provided URL
    const filters = {
      'course.name[0][field]': 'name',
      'course.name[0][value]': 'tes',
      'course.code[0][field]': 'code',
      'course.code[0][value]': 'test',
      'course.objective[0][field]': 'objectiveType',
      'course.objective[0][value][0]': 'REGULAR',
      'course.objective[0][value][1]': 'TRAINING',
      'course.isEnabled[0][field]': 'isEnabled',
      'course.isEnabled[0][value]': 'true',
      'course.contentProvider[0][field]': 'externalContentTypes',
      'course.contentProvider[0][value][0]': 'SELF',
      'course.contentProvider[0][value][1]': 'PLAN_PACKAGE',
      'course.contentProvider[0][value][2]': 'MARKETPLACE',
      'course.createdAt[0][field]': 'createdAt',
      'course.createdAt[0][value][0]': '2022-08-01T00:00:00.000Z',
      'course.createdAt[0][value][1]': '2027-08-31T00:00:00.000Z',
      'course.productSKUId[0][field]': 'productSKUId',
      'course.productSKUId[0][value]': '1',
      'course.isActivated[0][field]': 'isActivated',
      'course.isActivated[0][value]': 'true',
    };

    // Act
    const resp = await courseService.getCourseListWithFilters(filters);

    // Assert
    await expect(resp).toBeOK();
    const respJson = await resp.json();

    expect(respJson).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get course list successfully',
        data: expect.objectContaining({
          total: expect.any(Number),
          page: expect.any(Number),
          limit: expect.any(Number),
          items: expect.any(Array),
        }),
      }),
    );

    // Verify that returned courses match the filter criteria
    if (respJson.data.items.length > 0) {
      respJson.data.items.forEach((course: any) => {
        // Verify course structure matches expected response format
        expect(course).toEqual(
          expect.objectContaining({
            id: expect.any(String),
            contentProviderType: expect.any(String),
            externalContentTypes: expect.any(Array),
            objectiveType: expect.stringMatching(/^(REGULAR|TRAINING)$/),
            code: expect.any(String),
            isEnabled: expect.any(Boolean),
            isSelfEnrollEnabled: expect.any(Boolean),
            courseVersions: expect.objectContaining({
              id: expect.any(String),
              courseId: expect.any(String),
              status: expect.any(String),
              name: expect.any(String),
              isCertificateEnabled: expect.any(Boolean),
              createdAt: expect.any(String),
            }),
            isActivated: expect.any(Boolean),
          }),
        );

        // Verify filter criteria are applied
        expect(course.isEnabled).toBe(true);
        expect(course.isActivated).toBe(true);
        expect(['REGULAR', 'TRAINING']).toContain(course.objectiveType);
        expect(['SELF', 'PLAN_PACKAGE', 'MARKETPLACE']).toEqual(expect.arrayContaining(course.externalContentTypes));

        // Verify course name or code contains filter values (case insensitive)
        const nameMatch = course.courseVersions.name.toLowerCase().includes('tes');
        const codeMatch = course.code.toLowerCase().includes('test');
        expect(nameMatch || codeMatch).toBe(true);
      });
    }
  });

  // Step 1: Admin กรอกค้นหา "ชื่อหลักสูตร"
  test('@SKL-T20190-Step1 Admin search by course name should return courses with matching names', async ({
    configuration,
    authenticationService,
    courseService,
  }) => {
    // Arrange
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const filters = {
      'course.name[0][field]': 'name',
      'course.name[0][value]': 'TEST',
    };

    // Act
    const resp = await courseService.getCourseListWithFilters(filters);

    // Assert
    await expect(resp).toBeOK();
    const respJson = await resp.json();

    expect(respJson).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get course list successfully',
        data: expect.objectContaining({
          total: expect.any(Number),
          items: expect.any(Array),
        }),
      }),
    );

    // Verify filtered results contain the search term in course name
    if (respJson.data.items.length > 0) {
      respJson.data.items.forEach((course: any) => {
        expect(course.courseVersions.name.toLowerCase()).toContain('test');
      });
    }
  });

  // Step 2: Admin กรอกค้นหา "รหัสหลักสูตร"
  test('@SKL-T20190-Step2 Admin search by course code should return courses with matching codes', async ({
    configuration,
    authenticationService,
    courseService,
  }) => {
    // Arrange
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const filters = {
      'course.code[0][field]': 'code',
      'course.code[0][value]': 'TEST',
    };

    // Act
    const resp = await courseService.getCourseListWithFilters(filters);

    // Assert
    await expect(resp).toBeOK();
    const respJson = await resp.json();

    expect(respJson).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get course list successfully',
        data: expect.objectContaining({
          total: expect.any(Number),
          items: expect.any(Array),
        }),
      }),
    );

    // Verify filtered results contain the search term in course code
    if (respJson.data.items.length > 0) {
      respJson.data.items.forEach((course: any) => {
        expect(course.code.toLowerCase()).toContain('test');
      });
    }
  });

  // Step 3: Admin ติ๊กเลือก "วัตถุประสงค์หลักสูตร" - การเรียนทั่วไป
  test('@SKL-T20190-Step3a Admin filter by objective type REGULAR should return only REGULAR courses', async ({
    configuration,
    authenticationService,
    courseService,
  }) => {
    // Arrange
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const filters = {
      'course.objective[0][field]': 'objectiveType',
      'course.objective[0][value][0]': 'REGULAR',
    };

    // Act
    const resp = await courseService.getCourseListWithFilters(filters);

    // Assert
    await expect(resp).toBeOK();
    const respJson = await resp.json();

    expect(respJson).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get course list successfully',
        data: expect.objectContaining({
          total: expect.any(Number),
          items: expect.any(Array),
        }),
      }),
    );

    // Verify all returned courses have REGULAR objective type
    if (respJson.data.items.length > 0) {
      respJson.data.items.forEach((course: any) => {
        expect(course.objectiveType).toBe('REGULAR');
      });
    }
  });

  // Step 3: Admin ติ๊กเลือก "วัตถุประสงค์หลักสูตร" - การอบรม
  test('@SKL-T20190-Step3b Admin filter by objective type TRAINING should return only TRAINING courses', async ({
    configuration,
    authenticationService,
    courseService,
  }) => {
    // Arrange
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const filters = {
      'course.objective[0][field]': 'objectiveType',
      'course.objective[0][value][0]': 'TRAINING',
    };

    // Act
    const resp = await courseService.getCourseListWithFilters(filters);

    // Assert
    await expect(resp).toBeOK();
    const respJson = await resp.json();

    expect(respJson).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get course list successfully',
        data: expect.objectContaining({
          total: expect.any(Number),
          items: expect.any(Array),
        }),
      }),
    );

    // Verify all returned courses have TRAINING objective type
    if (respJson.data.items.length > 0) {
      respJson.data.items.forEach((course: any) => {
        expect(course.objectiveType).toBe('TRAINING');
      });
    }
  });

  // Step 4: Admin เลือก dropdown สถานะหลักสูตร - เปิดใช้งาน
  test('@SKL-T20190-Step4a Admin filter by enabled status should return only enabled courses', async ({
    configuration,
    authenticationService,
    courseService,
  }) => {
    // Arrange
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const filters = {
      'course.isEnabled[0][field]': 'isEnabled',
      'course.isEnabled[0][value]': 'true',
    };

    // Act
    const resp = await courseService.getCourseListWithFilters(filters);

    // Assert
    await expect(resp).toBeOK();
    const respJson = await resp.json();

    expect(respJson).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get course list successfully',
        data: expect.objectContaining({
          total: expect.any(Number),
          items: expect.any(Array),
        }),
      }),
    );

    // Verify all returned courses are enabled
    if (respJson.data.items.length > 0) {
      respJson.data.items.forEach((course: any) => {
        expect(course.isEnabled).toBe(true);
      });
    }
  });

  // Step 4: Admin เลือก dropdown สถานะหลักสูตร - ปิดใช้งาน
  test('@SKL-T20190-Step4b Admin filter by disabled status should return only disabled courses', async ({
    configuration,
    authenticationService,
    courseService,
  }) => {
    // Arrange
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const filters = {
      'course.isEnabled[0][field]': 'isEnabled',
      'course.isEnabled[0][value]': 'false',
    };

    // Act
    const resp = await courseService.getCourseListWithFilters(filters);

    // Assert
    await expect(resp).toBeOK();
    const respJson = await resp.json();

    expect(respJson).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get course list successfully',
        data: expect.objectContaining({
          total: expect.any(Number),
          items: expect.any(Array),
        }),
      }),
    );

    // Verify all returned courses are disabled
    if (respJson.data.items.length > 0) {
      respJson.data.items.forEach((course: any) => {
        expect(course.isEnabled).toBe(false);
      });
    }
  });

  // Step 6: Admin กรอกค้นหา "Product SKU ID"
  test('@SKL-T20190-Step6 Admin search by Product SKU ID should return exact matching courses', async ({
    configuration,
    authenticationService,
    courseService,
  }) => {
    // Arrange
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const filters = {
      'course.productSKUId[0][field]': 'productSKUId',
      'course.productSKUId[0][value]': '1',
    };

    // Act
    const resp = await courseService.getCourseListWithFilters(filters);

    // Assert
    await expect(resp).toBeOK();
    const respJson = await resp.json();

    expect(respJson).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get course list successfully',
        data: expect.objectContaining({
          total: expect.any(Number),
          items: expect.any(Array),
        }),
      }),
    );

    // Verify filtered results have exact Product SKU ID match
    if (respJson.data.items.length > 0) {
      respJson.data.items.forEach((course: any) => {
        expect(course.productSKUCourseId).toBe('1');
      });
    }
  });

  // Step 7: Admin เลือกช่วงวันที่เริ่ม-วันที่สิ้นสุด ของวันที่สร้าง
  test('@SKL-T20190-Step7 Admin filter by creation date range should return courses within date range', async ({
    configuration,
    authenticationService,
    courseService,
  }) => {
    // Arrange
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const filters = {
      'course.createdAt[0][field]': 'createdAt',
      'course.createdAt[0][value][0]': '2022-01-01T00:00:00.000Z',
      'course.createdAt[0][value][1]': '2027-12-31T23:59:59.999Z',
    };

    // Act
    const resp = await courseService.getCourseListWithFilters(filters);

    // Assert
    await expect(resp).toBeOK();
    const respJson = await resp.json();

    expect(respJson).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get course list successfully',
        data: expect.objectContaining({
          total: expect.any(Number),
          items: expect.any(Array),
        }),
      }),
    );

    // Verify filtered results are within the date range
    if (respJson.data.items.length > 0) {
      const startDate = new Date('2022-01-01T00:00:00.000Z');
      const endDate = new Date('2027-12-31T23:59:59.999Z');

      respJson.data.items.forEach((course: any) => {
        const courseCreatedAt = new Date(course.courseVersions.createdAt);
        expect(courseCreatedAt.getTime()).toBeGreaterThanOrEqual(startDate.getTime());
        expect(courseCreatedAt.getTime()).toBeLessThanOrEqual(endDate.getTime());
      });
    }
  });

  // Step 10: Admin เลือก dropdown ประกาศนียบัตร - มี
  test('@SKL-T20190-Step10a Admin filter by certificate enabled should return courses with certificates', async ({
    configuration,
    authenticationService,
    courseService,
  }) => {
    // Arrange
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const filters = {
      'course.certificate[0][field]': 'isCertificateEnabled',
      'course.certificate[0][value]': 'true',
    };

    // Act
    const resp = await courseService.getCourseListWithFilters(filters);

    // Assert
    await expect(resp).toBeOK();
    const respJson = await resp.json();

    expect(respJson).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get course list successfully',
        data: expect.objectContaining({
          total: expect.any(Number),
          items: expect.any(Array),
        }),
      }),
    );

    // Verify all returned courses have certificate enabled
    if (respJson.data.items.length > 0) {
      respJson.data.items.forEach((course: any) => {
        expect(course.courseVersions.isCertificateEnabled).toBe(true);
      });
    }
  });

  // Step 10: Admin เลือก dropdown ประกาศนียบัตร - ไม่มี
  test('@SKL-T20190-Step10b Admin filter by certificate disabled should return courses without certificates', async ({
    configuration,
    authenticationService,
    courseService,
  }) => {
    // Arrange
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const filters = {
      'course.certificate[0][field]': 'isCertificateEnabled',
      'course.certificate[0][value]': 'false',
    };

    // Act
    const resp = await courseService.getCourseListWithFilters(filters);

    // Assert
    await expect(resp).toBeOK();
    const respJson = await resp.json();

    expect(respJson).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get course list successfully',
        data: expect.objectContaining({
          total: expect.any(Number),
          items: expect.any(Array),
        }),
      }),
    );

    // Verify all returned courses have certificate disabled
    if (respJson.data.items.length > 0) {
      respJson.data.items.forEach((course: any) => {
        expect(course.courseVersions.isCertificateEnabled).toBe(false);
      });
    }
  });

  // Step 11: Admin เลือก dropdown ลงทะเบียนได้ด้วยตนเอง - เปิดใช้งาน
  test('@SKL-T20190-Step11a Admin filter by self enrollment enabled should return courses with self enrollment', async ({
    configuration,
    authenticationService,
    courseService,
  }) => {
    // Arrange
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const filters = {
      'course.selfEnroll[0][field]': 'isSelfEnrollEnabled',
      'course.selfEnroll[0][value]': 'true',
    };

    // Act
    const resp = await courseService.getCourseListWithFilters(filters);

    // Assert
    await expect(resp).toBeOK();
    const respJson = await resp.json();

    expect(respJson).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get course list successfully',
        data: expect.objectContaining({
          total: expect.any(Number),
          items: expect.any(Array),
        }),
      }),
    );

    // Verify all returned courses have self enrollment enabled
    if (respJson.data.items.length > 0) {
      respJson.data.items.forEach((course: any) => {
        expect(course.isSelfEnrollEnabled).toBe(true);
      });
    }
  });

  // Step 11: Admin เลือก dropdown ลงทะเบียนได้ด้วยตนเอง - ปิดใช้งาน
  test('@SKL-T20190-Step11b Admin filter by self enrollment disabled should return courses without self enrollment', async ({
    configuration,
    authenticationService,
    courseService,
  }) => {
    // Arrange
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const filters = {
      'course.selfEnroll[0][field]': 'isSelfEnrollEnabled',
      'course.selfEnroll[0][value]': 'false',
    };

    // Act
    const resp = await courseService.getCourseListWithFilters(filters);

    // Assert
    await expect(resp).toBeOK();
    const respJson = await resp.json();

    expect(respJson).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get course list successfully',
        data: expect.objectContaining({
          total: expect.any(Number),
          items: expect.any(Array),
        }),
      }),
    );

    // Verify all returned courses have self enrollment disabled
    if (respJson.data.items.length > 0) {
      respJson.data.items.forEach((course: any) => {
        expect(course.isSelfEnrollEnabled).toBe(false);
      });
    }
  });
});

test.describe('Permission', () => {
  test.skip('@SKL-T16376 Get Course return Error when Role Token is invalid', async ({
    configuration,
    authenticationService,
    courseService,
  }) => {
    // Arrange
    const user = configuration.usersLocal.user1;
    await authenticationService.login(user.email, user.password, user.organizationId);

    // Act
    const resp = await courseService.getAllCourse();

    // Assert
    const respJson = await resp.json();

    expect(resp).toBeOK();
    expect(respJson).toEqual(
      expect.objectContaining({
        status: '404',
        message: 'Unauthorized',
      }),
    );
  });

  test.skip('@SKL-T16377 Get Course return Error when Token is empty', async ({ courseService }) => {
    // Arrange

    // Act
    const resp = await courseService.getAllCourse(false);

    // Assert
    const respJson = await resp.json();

    expect(resp).toBeOK();
    expect(respJson).toEqual(
      expect.objectContaining({
        status: '404',
        message: 'Unauthorized',
      }),
    );
  });

  test(`User without permission get list of categories should return FORBIDDEN`, async ({
    configuration,
    authenticationService,
    courseService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const getCourseList = await courseService.getCourseList();
    await expect(getCourseList).not.toBeOK();
    const getCourseListResp = await getCourseList.json();
    expect(getCourseListResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });
});
