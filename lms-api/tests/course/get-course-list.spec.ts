import { expect } from '@playwright/test';
import { test } from '../../fixtures/default-fixture';

test.describe('Positive', () => {
  test.skip('@SKL-T16363 Get Course return Course list when token valid', async ({ courseService }) => {
    // Arrange

    // Act
    const resp = await courseService.getAllCourse();

    // Assert
    const respJson = await resp.json();
    await expect(resp).toBeOK();
    expect(respJson).toEqual(
      expect.objectContaining({
        message: 'Success',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              coursecode: expect.any(String),
            }),
          ]),
        }),
      }),
    );
  });

  const searchCases = [
    {
      caseId: '@SKL-T16372',
      course: 'ABC_001',
      case: 'course code',
    },
    {
      caseId: '@SKL-T16373',
      course: 'HAHAHAHAHAHA',
      case: 'course name',
    },
  ];
  for (const searchCase of searchCases) {
    test.skip(`${searchCase.caseId}Get Course return Course list when Search ${searchCase.case} is matched`, async ({
      courseService,
    }) => {
      // Arrange

      // Act
      const resp = await courseService.getCourseListBySearch(`${searchCase.course}`);

      // Assert
      const respJson = await resp.json();
      expect(resp).toBeOK();
      expect(respJson).toEqual(
        expect.objectContaining({
          message: 'Success',
          data: expect.objectContaining({
            items: expect.arrayContaining([
              expect.objectContaining({
                coursecode: 'ABC_001',
                coursename: 'HAHAHAHAHAHA',
              }),
            ]),
          }),
        }),
      );
    });
  }
  test.skip('@SKL-T16374 Get Course return no course data when Search data is not matched', async ({
    courseService,
  }) => {
    // Arrange

    // Act
    const resp = await courseService.getCourseListBySearch('Course is not exist');

    // Assert
    const respJson = await resp.json();
    await expect(resp).toBeOK();
    expect(respJson).toEqual(
      expect.objectContaining({
        message: 'Success',
        data: expect.objectContaining({
          items: expect.arrayContaining([]),
        }),
      }),
    );
  });

  test(`User with valid permission get list of course should return success`, async ({
    configuration,
    authenticationService,
    courseService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const getCourseList = await courseService.getCourseList();
    await expect(getCourseList).toBeOK();
    const getCourseListResp = await getCourseList.json();
    expect(getCourseListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.objectContaining({
          total: expect.any(Number),
        }),
      }),
    );
    expect(getCourseListResp.data.total).toBeGreaterThan(0);
  });

  test('@SKL-T20190 Admin filter ข้อมูลรายการหลักสูตร, แสดงผลรายการหลักสูตรตามการ filter', async ({
    configuration,
    authenticationService,
    courseService,
  }) => {
    // Arrange
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    // Define comprehensive filter parameters based on the provided URL
    const filters = {
      'course.name[0][field]': 'name',
      'course.name[0][value]': 'tes',
      'course.code[0][field]': 'code',
      'course.code[0][value]': 'test',
      'course.objective[0][field]': 'objectiveType',
      'course.objective[0][value][0]': 'REGULAR',
      'course.objective[0][value][1]': 'TRAINING',
      'course.isEnabled[0][field]': 'isEnabled',
      'course.isEnabled[0][value]': 'true',
      'course.contentProvider[0][field]': 'externalContentTypes',
      'course.contentProvider[0][value][0]': 'SELF',
      'course.contentProvider[0][value][1]': 'PLAN_PACKAGE',
      'course.contentProvider[0][value][2]': 'MARKETPLACE',
      'course.createdAt[0][field]': 'createdAt',
      'course.createdAt[0][value][0]': '2022-08-01T00:00:00.000Z',
      'course.createdAt[0][value][1]': '2027-08-31T00:00:00.000Z',
      'course.productSKUId[0][field]': 'productSKUId',
      'course.productSKUId[0][value]': '1',
      'course.isActivated[0][field]': 'isActivated',
      'course.isActivated[0][value]': 'true',
    };

    // Act
    const resp = await courseService.getCourseListWithFilters(filters);

    // Assert
    await expect(resp).toBeOK();
    const respJson = await resp.json();

    expect(respJson).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get course list successfully',
        data: expect.objectContaining({
          total: expect.any(Number),
          page: expect.any(Number),
          limit: expect.any(Number),
          items: expect.any(Array),
        }),
      }),
    );

    // Verify that returned courses match the filter criteria
    if (respJson.data.items.length > 0) {
      respJson.data.items.forEach((course: any) => {
        // Verify course structure matches expected response format
        expect(course).toEqual(
          expect.objectContaining({
            id: expect.any(String),
            contentProviderType: expect.any(String),
            externalContentTypes: expect.any(Array),
            objectiveType: expect.stringMatching(/^(REGULAR|TRAINING)$/),
            code: expect.any(String),
            isEnabled: expect.any(Boolean),
            isSelfEnrollEnabled: expect.any(Boolean),
            courseVersions: expect.objectContaining({
              id: expect.any(String),
              courseId: expect.any(String),
              status: expect.any(String),
              name: expect.any(String),
              isCertificateEnabled: expect.any(Boolean),
              createdAt: expect.any(String),
            }),
            isActivated: expect.any(Boolean),
          }),
        );

        // Verify filter criteria are applied
        expect(course.isEnabled).toBe(true);
        expect(course.isActivated).toBe(true);
        expect(['REGULAR', 'TRAINING']).toContain(course.objectiveType);
        expect(['SELF', 'PLAN_PACKAGE', 'MARKETPLACE']).toEqual(expect.arrayContaining(course.externalContentTypes));

        // Verify course name or code contains filter values (case insensitive)
        const nameMatch = course.courseVersions.name.toLowerCase().includes('tes');
        const codeMatch = course.code.toLowerCase().includes('test');
        expect(nameMatch || codeMatch).toBe(true);
      });
    }
  });

  test('@SKL-T20190-1 Admin filter by course name only should return filtered results', async ({
    configuration,
    authenticationService,
    courseService,
  }) => {
    // Arrange
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const filters = {
      'course.name[0][field]': 'name',
      'course.name[0][value]': 'TEST',
    };

    // Act
    const resp = await courseService.getCourseListWithFilters(filters);

    // Assert
    await expect(resp).toBeOK();
    const respJson = await resp.json();

    expect(respJson).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get course list successfully',
        data: expect.objectContaining({
          total: expect.any(Number),
          items: expect.any(Array),
        }),
      }),
    );

    // Verify filtered results contain the search term
    if (respJson.data.items.length > 0) {
      respJson.data.items.forEach((course: any) => {
        expect(course.courseVersions.name.toLowerCase()).toContain('test');
      });
    }
  });

  test('@SKL-T20190-2 Admin filter by objective type should return only matching courses', async ({
    configuration,
    authenticationService,
    courseService,
  }) => {
    // Arrange
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const filters = {
      'course.objective[0][field]': 'objectiveType',
      'course.objective[0][value][0]': 'REGULAR',
    };

    // Act
    const resp = await courseService.getCourseListWithFilters(filters);

    // Assert
    await expect(resp).toBeOK();
    const respJson = await resp.json();

    expect(respJson).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get course list successfully',
        data: expect.objectContaining({
          total: expect.any(Number),
          items: expect.any(Array),
        }),
      }),
    );

    // Verify all returned courses have REGULAR objective type
    if (respJson.data.items.length > 0) {
      respJson.data.items.forEach((course: any) => {
        expect(course.objectiveType).toBe('REGULAR');
      });
    }
  });

  test('@SKL-T20190-3 Admin filter by isEnabled should return only enabled courses', async ({
    configuration,
    authenticationService,
    courseService,
  }) => {
    // Arrange
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const filters = {
      'course.isEnabled[0][field]': 'isEnabled',
      'course.isEnabled[0][value]': 'true',
    };

    // Act
    const resp = await courseService.getCourseListWithFilters(filters);

    // Assert
    await expect(resp).toBeOK();
    const respJson = await resp.json();

    expect(respJson).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get course list successfully',
        data: expect.objectContaining({
          total: expect.any(Number),
          items: expect.any(Array),
        }),
      }),
    );

    // Verify all returned courses are enabled
    if (respJson.data.items.length > 0) {
      respJson.data.items.forEach((course: any) => {
        expect(course.isEnabled).toBe(true);
      });
    }
  });
});

test.describe('Permission', () => {
  test.skip('@SKL-T16376 Get Course return Error when Role Token is invalid', async ({
    configuration,
    authenticationService,
    courseService,
  }) => {
    // Arrange
    const user = configuration.usersLocal.user1;
    await authenticationService.login(user.email, user.password, user.organizationId);

    // Act
    const resp = await courseService.getAllCourse();

    // Assert
    const respJson = await resp.json();

    expect(resp).toBeOK();
    expect(respJson).toEqual(
      expect.objectContaining({
        status: '404',
        message: 'Unauthorized',
      }),
    );
  });

  test.skip('@SKL-T16377 Get Course return Error when Token is empty', async ({ courseService }) => {
    // Arrange

    // Act
    const resp = await courseService.getAllCourse(false);

    // Assert
    const respJson = await resp.json();

    expect(resp).toBeOK();
    expect(respJson).toEqual(
      expect.objectContaining({
        status: '404',
        message: 'Unauthorized',
      }),
    );
  });

  test(`User without permission get list of categories should return FORBIDDEN`, async ({
    configuration,
    authenticationService,
    courseService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const getCourseList = await courseService.getCourseList();
    await expect(getCourseList).not.toBeOK();
    const getCourseListResp = await getCourseList.json();
    expect(getCourseListResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });
});
