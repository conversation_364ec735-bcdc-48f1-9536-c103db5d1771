import { expect } from '@playwright/test';
import { test } from '../../fixtures/default-fixture';

test.describe('Positive', () => {
  test(`User with valid permission get classroom list should return SUCCESS`, async ({
    configuration,
    authenticationService,
    classroomEnrollmentService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const getClassroomEnrollmentList = await classroomEnrollmentService.getClassroomEnrollmentList();
    await expect(getClassroomEnrollmentList).toBeOK();
    const getClassroomEnrollmentListResp = await getClassroomEnrollmentList.json();
    expect(getClassroomEnrollmentListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.objectContaining({
          total: expect.any(Number),
        }),
      }),
    );
    expect(getClassroomEnrollmentListResp.data.total).toBeGreaterThan(0);
  });
});

test.describe('Permission', () => {
  test(`User without valid permission get classroom list should return FORBIDDEN`, async ({
    configuration,
    authenticationService,
    classroomEnrollmentService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const getClassroomEnrollmentList = await classroomEnrollmentService.getClassroomEnrollmentList();
    await expect(getClassroomEnrollmentList).not.toBeOK();
    const getClassroomEnrollmentListResp = await getClassroomEnrollmentList.json();
    expect(getClassroomEnrollmentListResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });
});
