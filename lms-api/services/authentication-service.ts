import { APIResponse } from '@playwright/test';
import { APIContextManager } from '../../shared/api-request/api-context-manager';
import { BaseAPIService } from '../../shared/api-request/base-api-service';
import { Configuration } from './configuration';

export class AuthenticationService extends BaseAPIService {
  endpointLogout: string;
  endpointLogin: string;
  endpointSetPassword: string;
  endpointRecoveryPassword: string;
  endpointForgotPassword: string;
  endpointVerifyRecoveryToken: string;

  constructor(contextManager: APIContextManager, configuration: Configuration) {
    super(contextManager, configuration);
    this.endpoint = '/authentication-v1';
    this.endpointLogin = '/validate';
    this.endpointLogout = '/logout';
    this.endpointSetPassword = '/set-password';
    this.endpointRecoveryPassword = '/recovery-password';
    this.endpointForgotPassword = '/forgot-password';
    this.endpointVerifyRecoveryToken = '/verify-recovery-token';
  }

  async login(username: string, password: string, organizationId: string, autoParseToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .post(`${this.baseApiUrl}${this.endpoint}${this.endpointLogin}`, {
        headers: this.getHeaderForBaseUrl(false),
        data: {
          username: username,
          password: password,
          organizationId: organizationId,
        },
      });

    if (autoParseToken) {
      const respJson = await resp.json();
      this.contextManager.setAccessToken(respJson.data.accessToken);
      this.contextManager.setRefreshToken(respJson.data.refreshToken);
    }
    return resp;
  }

  async logout(redirectUrl: string, withToken = true): Promise<APIResponse> {
    const latestUrl = this.contextManager.getLatestUrl();
    const resp = await this.contextManager
      .getContext()
      .post(`${this.baseApiUrl}${this.endpoint}${this.endpointLogout}?redirectUrl=${redirectUrl}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          latestUrl: latestUrl,
          redirectUrl: redirectUrl,
        },
      });
    return resp;
  }

  async newPassword(recoveryToken: string, newPassword: string): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .post(`${this.baseApiUrl}${this.endpoint}${this.endpointSetPassword}`, {
        headers: this.getHeaderForBaseUrl(false),
        data: {
          recoveryToken: recoveryToken,
          newPassword: newPassword,
        },
      });
    return resp;
  }

  async recoveryPassword(recoveryToken: string, newPassword: string): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .post(`${this.baseApiUrl}${this.endpoint}${this.endpointRecoveryPassword}`, {
        headers: this.getHeaderForBaseUrl(false),
        data: {
          recoveryToken: recoveryToken,
          newPassword: newPassword,
        },
      });
    return resp;
  }

  async forgotPassword(username: string): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .post(`${this.baseApiUrl}${this.endpoint}${this.endpointForgotPassword}`, {
        headers: this.getHeaderForBaseUrl(false),
        data: {
          username: username,
        },
      });
    return resp;
  }

  async verifyRecoveryToken(recoveryToken: string): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .post(`${this.baseApiUrl}${this.endpoint}${this.endpointVerifyRecoveryToken}`, {
        headers: this.getHeaderForBaseUrl(false),
        data: {
          recoveryToken: recoveryToken,
        },
      });
    return resp;
  }
}
