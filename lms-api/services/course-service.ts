import { APIResponse } from '@playwright/test';
import { APIContextManager } from '../../shared/api-request/api-context-manager';
import { BaseAPIService } from '../../shared/api-request/base-api-service';
import { Configuration } from './configuration';

export class CourseService extends BaseAPIService {
  endpointCourse: string;
  endpointCertificateV2: string;
  endpointCertificates: string;
  endpointCourseVersions: string;
  endpointCourseItems: string;
  endpointCourseItemsV1: string;

  constructor(contextManager: APIContextManager, configuration: Configuration) {
    super(contextManager, configuration);
    this.endpoint = '/courses-v1';
    this.endpointCourse = '/courses';
    this.endpointCertificateV2 = '/certificate-v2';
    this.endpointCertificates = '/certificates';
    this.endpointCourseVersions = '/course-versions';
    this.endpointCourseItems = '/course-items';
    this.endpointCourseItemsV1 = '/course-items-v1';
  }

  async getCourseList(withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .get(`${this.baseApiUrl}${this.endpoint}${this.endpointCourse}`, {
        headers: this.getHeaderForBaseUrl(withToken),
      });
    return resp;
  }

  async getCourseDetail(id: string, withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .get(`${this.baseApiUrl}${this.endpoint}${this.endpointCourse}/${id}`, {
        headers: this.getHeaderForBaseUrl(withToken),
      });
    return resp;
  }

  async createCourse(
    code: string,
    contentType: string,
    name: string,
    objectiveType: string,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .post(`${this.baseApiUrl}${this.endpoint}${this.endpointCourse}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          code: code,
          contentType: contentType,
          name: name,
          objectiveType: objectiveType,
        },
      });
    return resp;
  }

  async updateCourseGeneralInfo(
    courseId: string,
    code: string,
    name: string,
    version: number,
    image: string = '',
    description?: string,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpoint}${this.endpointCourse}/${courseId}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          code: code,
          name: name,
          version: version,
          description: description,
          image: image || null,
        },
      });

    return resp;
  }

  async getCourseListBySearch(search: string, withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager.getContext().get(`${this.baseApiUrl}${this.endpoint}?keyword=${search}`, {
      headers: this.getHeaderForBaseUrl(withToken),
    });
    return resp;
  }

  async getCourseListWithFilters(filters: Record<string, any>, withToken = true): Promise<APIResponse> {
    const queryParams = new URLSearchParams();

    // Build query parameters from filters object
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value)) {
          value.forEach((item, index) => {
            queryParams.append(`${key}[${index}]`, item);
          });
        } else {
          queryParams.append(key, value);
        }
      }
    });

    const url = `${this.baseApiUrl}${this.endpoint}${this.endpointCourse}?${queryParams.toString()}`;
    const resp = await this.contextManager.getContext().get(url, {
      headers: this.getHeaderForBaseUrl(withToken),
    });
    return resp;
  }

  async updateAllCourseDetail(
    courseId: number,
    courseCode: string,
    courseName: string,
    description: string,
    thumbnail: string,
    active: boolean,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager.getContext().put(`${this.baseApiUrl}${this.endpoint}/${courseId}`, {
      headers: this.getHeaderForBaseUrl(withToken),
      data: {
        courseCode: courseCode,
        name: courseName,
        description: description,
        filePath: thumbnail,
        active: active,
      },
    });

    return resp;
  }

  async updateCategoryCourse(courseId: number, categoryId: any, withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager.getContext().put(`${this.baseApiUrl}${this.endpoint}/${courseId}`, {
      headers: this.getHeaderForBaseUrl(withToken),
      data: {
        categoryId: categoryId,
      },
    });

    return resp;
  }

  async updateEnrollmentSetting(
    courseId: number,
    enrollQuota: any,
    enrollSelf: boolean,
    enrollPeriod: any,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager.getContext().put(`${this.baseApiUrl}${this.endpoint}/${courseId}`, {
      headers: this.getHeaderForBaseUrl(withToken),
      data: {
        enrollQuota: enrollQuota,
        enrollSelf: enrollSelf,
        enrollPeriod: enrollPeriod,
      },
    });

    return resp;
  }

  async updateInstructor(
    courseId: number,
    instructor: string[],
    version: number,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .post(`${this.baseApiUrl}${this.endpoint}${this.endpointCourse}/${courseId}/instructors`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          version: version,
          instructorIds: instructor,
        },
      });

    return resp;
  }

  async updateAccess(
    courseId: string,
    userGroupIds: string[],
    accessType: string,
    version: number,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpoint}${this.endpointCourse}/${courseId}/access`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          accessType: accessType,
          userGroupIds: userGroupIds,
          version: version,
        },
      });

    return resp;
  }

  async updateCourseRegistrationSetting(
    courseId: string,
    version: number,
    isSelfEnrollEnabled: boolean,
    withToken?: boolean,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpoint}${this.endpointCourse}/${courseId}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          isSelfEnrollEnabled,
          version,
        },
      });

    return resp;
  }

  async updateCourseExpiryDaySetting(
    courseId: string,
    version: number,
    expiryDay: number,
    withToken?: boolean,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpoint}${this.endpointCourse}/${courseId}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          expiryDay,
          version,
        },
      });

    return resp;
  }

  async updateCourseReRegisterAfterCompleteSetting(
    courseId: string,
    version: number,
    isReEnrollEnabled: boolean,
    reEnrollDay: number,
    withToken?: boolean,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpoint}${this.endpointCourse}/${courseId}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          isReEnrollEnabled,
          reEnrollDay,
          version,
        },
      });

    return resp;
  }

  async updateCourseReRegisterAfterExpiresSetting(
    courseId: string,
    version: number,
    isReEnrollExpireEnabled: boolean,
    reEnrollExpireDay: number,
    withToken?: boolean,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpoint}${this.endpointCourse}/${courseId}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          isReEnrollExpireEnabled,
          reEnrollExpireDay,
          version,
        },
      });

    return resp;
  }

  async updateCourseEnrollment({
    isSelfEnrollEnabled,
    courseId,
    version,
    expiryDay,
    isReEnrollEnabled,
    reEnrollDay,
    isReEnrollExpireEnabled,
    reEnrollExpireDay,
    isCountdownArticle,
    isPlayVideoBackground,
    isSeekEnabled,
    isVideoSpeedEnabled,
    isAttentionCheckEnabled,
    isLearnableFullscreen,
    withToken = true,
  }: {
    courseId: string;
    version: number;
    isSelfEnrollEnabled?: boolean;
    expiryDay?: number;
    isReEnrollEnabled?: boolean;
    reEnrollDay?: number;
    isReEnrollExpireEnabled?: boolean;
    reEnrollExpireDay?: number;
    isCountdownArticle?: boolean;
    isPlayVideoBackground?: boolean;
    isSeekEnabled?: boolean;
    isVideoSpeedEnabled?: boolean;
    isAttentionCheckEnabled?: boolean;
    isLearnableFullscreen?: boolean;
    withToken?: boolean;
  }): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpoint}${this.endpointCourse}/${courseId}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          isSelfEnrollEnabled,
          version,
          expiryDay,
          isReEnrollEnabled,
          reEnrollDay,
          isReEnrollExpireEnabled,
          reEnrollExpireDay,
          isCountdownArticle,
          isPlayVideoBackground,
          isSeekEnabled,
          isVideoSpeedEnabled,
          isAttentionCheckEnabled,
          isLearnableFullscreen,
        },
      });

    return resp;
  }

  async createCertificate(
    courseVersionId: string,
    isSent: boolean,
    organizationCertificateId: string,
    type: string,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .post(`${this.baseApiUrl}${this.endpointCertificateV2}${this.endpointCertificates}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          courseVersionId: courseVersionId,
          isSent: isSent,
          payload: { organizationCertificateId: organizationCertificateId, type: type },
        },
      });
    return resp;
  }

  async updateCertificate(
    courseVersionId: string,
    isCertificateEnabled: boolean,
    isMultipleCertificate?: boolean,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(
        `${this.baseApiUrl}${this.endpoint}${this.endpointCourseVersions}/${courseVersionId}${this.endpointCertificates}`,
        {
          headers: this.getHeaderForBaseUrl(withToken),
          data: {
            isCertificateEnabled,
            isMultipleCertificate,
          },
        },
      );
    return resp;
  }

  async updateEnrollType(
    courseId: number,
    enrollType: string,
    version: number,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpoint}${this.endpointCourse}/${courseId}/enroll-type`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          enrollType: enrollType,
          version: version,
        },
      });

    return resp;
  }

  async createCoursePart(id: number, name: string, withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .post(`${this.baseApiUrl}${this.endpoint}${this.endpointCourseVersions}/${id}/parts`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          name: name,
        },
      });

    return resp;
  }

  async createCourseItem(
    courseVersionId: number,
    name: string,
    partId: string,
    type: string,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .post(`${this.baseApiUrl}${this.endpointCourseItemsV1}${this.endpointCourseItems}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          courseVersionId: courseVersionId,
          name: name,
          partId: partId,
          type: type,
        },
      });

    return resp;
  }

  async updateCourseItemQuiz({
    code,
    courseVersionId,
    courseItemId,
    isEnabled,
    name,
    type,
    quizName,
    testType,
    materialMediaId,
    questions = [],
    quizDescription = '',
    description = '',
    retestOption,
    retestEnabled,
    retestCount,
    limitTimeDurationEnabled,
    totalDurationSec,
    showAnswerEnabled,
    afterQuestionSubmit,
    afterQuizSubmit,
    withToken = true,
  }: {
    code: string;
    courseVersionId: string;
    courseItemId: string;
    isEnabled: boolean;
    name: string;
    type: string;
    quizName: string;
    testType: string;
    materialMediaId?: string;
    questions?: any[];
    quizDescription?: string;
    description?: string;
    retestOption?: string;
    retestEnabled?: boolean;
    retestCount?: number | null;
    limitTimeDurationEnabled?: boolean;
    totalDurationSec?: number | null;
    showAnswerEnabled?: boolean;
    afterQuestionSubmit?: {
      isEnabled: boolean;
      isShowCorrectAnswer: boolean;
    };
    afterQuizSubmit?: {
      isEnabled: boolean;
      type: string | null;
      isShowCorrectAnswer: boolean;
    };
    withToken?: boolean;
  }): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpointCourseItemsV1}${this.endpointCourseItems}/${courseItemId}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          code: code,
          courseVersionId: courseVersionId,
          description: description,
          id: courseItemId,
          isEnabled: isEnabled,
          materialMediaId: materialMediaId,
          name: name,
          quiz: {
            name: quizName,
            description: quizDescription,
            testType: testType,
            retest: {
              isEnabled: retestEnabled,
              totalCountRetest: retestCount,
            },
            limitTimeDuration: {
              isEnabled: limitTimeDurationEnabled,
              totalDurationSec: totalDurationSec,
            },
            showAnswer: {
              isEnabled: showAnswerEnabled,
              afterQuestionSubmit: afterQuestionSubmit,
              afterQuizSubmit: afterQuizSubmit,
            },
            questions: questions,
          },
          type: type,
        },
      });

    return resp;
  }

  async updateCourseItemArticle(
    code: string,
    courseVersionId: number,
    courseItemId: string,
    isEnabled: boolean,
    name: string,
    type: string,
    contentHtml: string,
    duration: number,
    materialMediaId?: string,
    attachments?: [],
    description?: string,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpointCourseItemsV1}${this.endpointCourseItems}/${courseItemId}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          article: { contentHtml: contentHtml, duration: duration },
          attachments: attachments,
          code: code,
          courseVersionId: courseVersionId,
          description: description,
          id: courseItemId,
          isEnabled: isEnabled,
          materialMediaId: materialMediaId,
          name: name,
          type: type,
        },
      });

    return resp;
  }

  async updateCourseItemSurvey({
    courseVersionId,
    courseItemId,
    surveyCode,
    courseItemType,
    isEnabled,
    contentHtml,
    allowCompleteSurveyAutomatic,
    surveyCompleteText,
    surveyDescription,
    goNextPageAutomatic,
    locale,
    firstChoice,
    secondChoice,
    hideNumber,
    isRequired,
    surveyQuestionName,
    choiceTitle,
    type,
    sectionName,
    sectionTitle,
    questionsOnPageMode,
    showProgressBar,
    surveyTitle,
    surveyName,
    surveyType,
    updatedAt,
    description = '',
    materialMediaId = '',
  }: {
    courseVersionId: string;
    courseItemId: string;
    surveyCode: string;
    courseItemType: string;
    isEnabled: boolean;
    contentHtml: string;
    allowCompleteSurveyAutomatic: boolean;
    surveyCompleteText: string;
    surveyDescription: string;
    goNextPageAutomatic: boolean;
    locale: string;
    firstChoice: object;
    secondChoice: object;
    hideNumber: boolean;
    isRequired: boolean;
    surveyQuestionName: string;
    choiceTitle: string;
    type: string;
    sectionName: string;
    sectionTitle: string;
    questionsOnPageMode: string;
    showProgressBar: string;
    surveyTitle: string;
    surveyName: string;
    surveyType: string;
    updatedAt: string;
    description?: string;
    materialMediaId?: string;
  }): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpointCourseItemsV1}${this.endpointCourseItems}/${courseItemId}`, {
        headers: this.getHeaderForBaseUrl(true),
        data: {
          code: surveyCode,
          courseVersionId: courseVersionId,
          description: description,
          id: courseItemId,
          isEnabled: isEnabled,
          materialMediaId: materialMediaId,
          name: surveyName,
          survey: {
            contentHtml: contentHtml,
            formSchema: {
              allowCompleteSurveyAutomatic: allowCompleteSurveyAutomatic,
              description: surveyDescription,
              goNextPageAutomatic: goNextPageAutomatic,
              locale: locale,
              pages: [
                {
                  name: sectionName,
                  title: sectionTitle,
                  elements: [
                    {
                      type: surveyType,
                      name: surveyQuestionName,
                      title: choiceTitle,
                      hideNumber: hideNumber,
                      isRequired: isRequired,
                      choices: [firstChoice, secondChoice],
                    },
                  ],
                },
              ],
              questionsOnPageMode: questionsOnPageMode,
              showProgressBar: showProgressBar,
              title: surveyTitle,
            },
          },
          type: courseItemType,
          updatedAt: updatedAt,
        },
      });

    return resp;
  }

  async updateCourseItemClassroom(
    code: string,
    courseVersionId: number,
    courseItemId: string,
    isEnabled: boolean,
    name: string,
    type: string,
    contentHtml: string,
    attachments?: [],
    description?: string,
    materialMediaId?: string,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpointCourseItemsV1}${this.endpointCourseItems}/${courseItemId}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          attachments: attachments,
          classroom: { contentHtml: contentHtml },
          code: code,
          courseVersionId: courseVersionId,
          description: description,
          id: courseItemId,
          isEnabled: isEnabled,
          materialMediaId: materialMediaId,
          name: name,
          type: type,
        },
      });

    return resp;
  }

  async updateCoursePart(
    courseVersionId: number,
    coursePartId: string,
    coursePartName: string,
    description: string,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(
        `${this.baseApiUrl}${this.endpoint}${this.endpointCourseVersions}/${courseVersionId}/parts/${coursePartId}`,
        {
          headers: this.getHeaderForBaseUrl(withToken),
          data: {
            description: description,
            name: coursePartName,
            partId: coursePartId,
          },
        },
      );

    return resp;
  }

  async deleteCourseItem(courseItemId: string, withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .delete(`${this.baseApiUrl}${this.endpointCourseItemsV1}${this.endpointCourseItems}/${courseItemId}`, {
        headers: this.getHeaderForBaseUrl(withToken),
      });

    return resp;
  }

  async deleteCoursePart(courseVersionId: number, coursePartId: string, withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .delete(
        `${this.baseApiUrl}${this.endpoint}${this.endpointCourseVersions}/${courseVersionId}/parts/${coursePartId}`,
        {
          headers: this.getHeaderForBaseUrl(withToken),
        },
      );

    return resp;
  }

  async deleteCourse(courseId: number, withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .delete(`${this.baseApiUrl}${this.endpoint}${this.endpointCourseVersions}/${courseId}`, {
        headers: this.getHeaderForBaseUrl(withToken),
      });

    return resp;
  }
}
