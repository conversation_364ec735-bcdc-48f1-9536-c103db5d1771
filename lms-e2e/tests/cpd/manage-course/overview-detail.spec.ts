import { expect } from '@playwright/test';
import { test } from '../../../fixtures/default-fixture';
import { ObjectiveCourse } from '../../../elements/pages/backoffice/create-course-elements';

test.beforeEach(async ({ courseVersionsRepo, configuration, coursesRepo }) => {
  const tsiCourseName = '[Automate][TEMP] Admin create TSI course';
  const tsiCourseCode = 'AUTOMATE_TSI_TEMP';

  const courseVersions = [
    configuration.shareCourses.regularCourseVersion.courseVersions[2],
    configuration.shareCourses.regularCourseVersion.courseVersions[3],
    { name: tsiCourseName },
  ];

  for (const versions of courseVersions) {
    await courseVersionsRepo.deleteByCourseName(versions.name);
  }

  await coursesRepo.deleteCourseByCode(tsiCourseCode);
});

test.afterEach(async ({ courseVersionsRepo, configuration, coursesRepo }) => {
  const tsiCourseName = '[Automate][TEMP] Admin create TSI course';
  const tsiCourseCode = 'AUTOMATE_TSI_TEMP';

  const regularCourseNewVersion = [
    configuration.shareCourses.regularCourseVersion.courseVersions[2],
    configuration.shareCourses.regularCourseVersion.courseVersions[3],
    { name: tsiCourseName },
  ];

  for (const versions of regularCourseNewVersion) {
    await courseVersionsRepo.deleteByCourseName(versions.name);
  }

  await coursesRepo.deleteCourseByCode(tsiCourseCode);
});

test.describe('Admin - Course Management (CPD)', () => {
  test('@SKL-T20189 Admin เข้าถึงหน้าหลักสูตรทั้งหมด, แสดงหน้าตารางรายการหลักสูตรทั้งหมด', async ({
    configuration,
    manageCourseListPage,
    adminDashboardPage,
    homePage,
    loginCpdPage,
    loginSSOPage,
    manageCourseDetailPage,
  }) => {
    const adminCPD = configuration.shareUsers.userAdminCreateUserCPD;
    const courseName = '[Automate][TEMP] Admin create TSI course';
    const courseCode = 'AUTOMATE_TSI_TEMP';

    // Admin login
    await loginCpdPage.accessLoginSSO();
    await loginSSOPage.inputUserName(adminCPD.citizenId);
    await loginSSOPage.inputPassword(adminCPD.password);
    await loginSSOPage.submit();
    await expect(homePage.userProfileLocator).toBeVisible();

    await adminDashboardPage.accessCoursesMenu();

    await expect(manageCourseListPage.courseRowsLocator.first()).toBeVisible();
    await expect(manageCourseListPage.searchInputLocator).toBeVisible();
    await expect(manageCourseListPage.searchSubmitBtnLocator).toBeVisible();
    await expect(manageCourseListPage.createCourseButtonLocator).toBeVisible();

    // @SKL-T20192 Admin สร้างหลักสูตรการอบรมวิชาชีพการลงทุน (TSI) และกรอกข้อมูลถูกต้อง, สร้างหลักสูตรสำเร็จ
    await manageCourseListPage.createCourse();
    await manageCourseListPage.createCourseForm.selectObjectiveCourse(ObjectiveCourse.TSI);
    await manageCourseListPage.createCourseForm.fillCourseCode(courseCode);
    await manageCourseListPage.createCourseForm.fillCourseName(courseName);
    await manageCourseListPage.createCourseForm.submitCreateCourseButton();
    await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('สร้างหลักสูตรสำเร็จ');

    // @SKL-T20201 Admin แก้ไขข้อมูลคุณสมบัติหลักสูตรของหลักสูตรการอบรมวิชาชีพการลงทุน (TSI) กรณีกรอกไม่ข้อมูลถูกต้อง, แสดง error และไม่สามารถบันทึกได้
    // Access edit course properties
    await manageCourseDetailPage.editCoursePropertiesButtonLocator.click();

    // Try to save without filling required fields
    await manageCourseDetailPage.saveButtonLocator.click();

    // Validate error messages when empty (TSI only has 2 fields)
    await expect(manageCourseDetailPage.trainingCenterErrorLocator).toContainText('กรุณาเลือกศูนย์อบรม');
    await expect(manageCourseDetailPage.applicantTypeErrorLocator).toContainText('กรุณาเลือกประเภทผู้สมัคร');

    // @SKL-T20199 Admin แก้ไขข้อมูลคุณสมบัติหลักสูตรของหลักสูตรการอบรมวิชาชีพการลงทุน (TSI) กรณีกรอกข้อมูลถูกต้อง, สามารถแก้ไขได้และบันทึกสำเร็จ
    // Fill course properties form with correct data for TSI (only 2 fields)
    await manageCourseDetailPage.fillTSICoursePropertiesForm();

    // Save the form
    await manageCourseDetailPage.saveButtonLocator.click();

    // Validate success toast message
    await expect(manageCourseDetailPage.saveSuccessToastLocator).toBeVisible();
  });

  test('SKL-T20253 Admin ลบหลักสูตรแบบร่างเวอร์ชัน 1 ที่ sync มาจาก 3rd party, ไม่แสดงปุ่มลบ', async ({
    configuration,
    manageCourseListPage,
    adminDashboardPage,
    homePage,
    loginCpdPage,
    loginSSOPage,
    manageCourseDetailPage,
  }) => {
    const adminCPD = configuration.shareUsers.userAdminCreateUserCPD;
    const syncedCourse = configuration.shareCourses.syncedTsiCourse;
    // Admin login
    await loginCpdPage.accessLoginSSO();
    await loginSSOPage.inputUserName(adminCPD.citizenId);
    await loginSSOPage.inputPassword(adminCPD.password);
    await loginSSOPage.submit();
    await expect(homePage.userProfileLocator).toBeVisible();

    await adminDashboardPage.accessManageCourses();
    await manageCourseListPage.searchCourseName(syncedCourse.courseVersions[1].name);
    await manageCourseListPage.viewCourseDetail();

    await expect(manageCourseDetailPage.generalInfoCourseNameDisplayLocator).toContainText(
      syncedCourse.courseVersions[1].name,
    );
    await expect(manageCourseDetailPage.deleteDraftVersionButtonLocator).not.toBeVisible();
  });

  test('SKL-T20205 Admin แก้ไขผู้สอนหลักสูตรการอบรม ที่ sync มาจาก 3rd party, ไม่มีปุ่มแก้ไขและไม่สามารถแก้ไขได้', async ({
    configuration,
    manageCourseListPage,
    adminDashboardPage,
    homePage,
    loginCpdPage,
    loginSSOPage,
    manageCourseDetailPage,
  }) => {
    const adminCPD = configuration.shareUsers.userAdminCreateUserCPD;
    const syncedCourse = configuration.shareCourses.syncedTsiCourse;
    // Admin login
    await loginCpdPage.accessLoginSSO();
    await loginSSOPage.inputUserName(adminCPD.citizenId);
    await loginSSOPage.inputPassword(adminCPD.password);
    await loginSSOPage.submit();
    await expect(homePage.userProfileLocator).toBeVisible();

    await adminDashboardPage.accessManageCourses();
    await manageCourseListPage.searchCourseName(syncedCourse.courseVersions[1].name);
    await manageCourseListPage.viewCourseDetail();

    await manageCourseDetailPage.instructorMenuLocator.click();
    await expect(manageCourseDetailPage.editInstructorButtonLocator).not.toBeVisible();
  });

  test('SKL-T20196 Admin แก้ไขข้อมูลภาพรวมของหลักสูตรการอบรม ที่ sync มาจาก 3rd party กรณีกรอกข้อมูลถูกต้อง, สามารถแก้ไขได้', async ({
    configuration,
    manageCourseListPage,
    adminDashboardPage,
    homePage,
    loginCpdPage,
    loginSSOPage,
    manageCourseDetailPage,
  }) => {
    const adminCPD = configuration.shareUsers.userAdminCreateUserCPD;
    const syncedCourse = configuration.shareCourses.syncedTsiCourse;
    // Admin login
    await loginCpdPage.accessLoginSSO();
    await loginSSOPage.inputUserName(adminCPD.citizenId);
    await loginSSOPage.inputPassword(adminCPD.password);
    await loginSSOPage.submit();
    await expect(homePage.userProfileLocator).toBeVisible();
    await adminDashboardPage.accessManageCourses();
    await manageCourseListPage.searchCourseName(syncedCourse.courseVersions[1].name);
    await manageCourseListPage.viewCourseDetail();

    await manageCourseDetailPage.accessTabGeneralInfo();
    await manageCourseDetailPage.editGeneralInfoButtonLocator.click();
    await manageCourseDetailPage.courseCodeInputLocator.fill('AUTOMATE_TEST_CODE');
    await manageCourseDetailPage.saveButtonLocator.click();
    await expect(manageCourseDetailPage.saveSuccessToastLocator).toBeVisible();
    await manageCourseDetailPage.saveSuccessToastLocator.waitFor({ state: 'hidden' });

    await manageCourseDetailPage.editGeneralInfoButtonLocator.click();
    await manageCourseDetailPage.courseCodeInputLocator.fill(syncedCourse.courseCode);
    await manageCourseDetailPage.saveButtonLocator.click();
    await expect(manageCourseDetailPage.saveSuccessToastLocator).toBeVisible();

    await expect(manageCourseDetailPage.saveSuccessToastLocator).toBeVisible();
  });
});
